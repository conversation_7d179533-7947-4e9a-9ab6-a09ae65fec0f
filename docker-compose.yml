version: '3.8'

services:
  sow-generator:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      # Add your environment variables here
      # - GEMINI_API_KEY=${GEMINI_API_KEY}
      # - DATABASE_URL=${DATABASE_URL}
    volumes:
      # Mount uploads directory for persistent file storage
      - ./uploads:/app/uploads
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:8080/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a reverse proxy for production
  # nginx:
  #   image: nginx:alpine
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   volumes:
  #     - ./nginx.conf:/etc/nginx/nginx.conf
  #   depends_on:
  #     - sow-generator
  #   restart: unless-stopped

volumes:
  uploads:
    driver: local
