import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { readFile } from 'fs/promises';
import { join } from 'path';

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

export async function POST(request: NextRequest) {
  let markdown = '';

  try {
    console.log('🔍 GEMINI TEMPLATE ANALYSIS: Starting intelligent template scan...');

    const requestData = await request.json();
    const { templateId } = requestData;
    markdown = requestData.markdown;

    if (!templateId || !markdown) {
      return NextResponse.json({ error: 'Missing template ID or markdown content' }, { status: 400 });
    }

    console.log('Template ID:', templateId);
    console.log('Markdown length:', markdown.length);

    // Load company settings for context
    const settings = await loadCompanySettings();

    // Analyze template with Gemini AI
    const analysis = await analyzeTemplateWithGemini(markdown, settings);

    console.log('✅ GEMINI ANALYSIS: Template analysis complete');
    console.log('Required fields detected:', analysis.requiredFields.length);
    console.log('Optional fields detected:', analysis.optionalFields.length);
    console.log('Auto-fillable fields detected:', analysis.autoFillableFields?.length || 0);

    // Debug: Log categories of detected fields
    console.log('🔍 DEBUG: Required field categories:', analysis.requiredFields.map(f => f.category));
    console.log('🔍 DEBUG: Optional field categories:', analysis.optionalFields.map(f => f.category));
    console.log('🔍 DEBUG: Auto-fillable field categories:', analysis.autoFillableFields?.map(f => f.category) || []);

    return NextResponse.json({
      templateId,
      analysis: {
        requiredFields: analysis.requiredFields,
        optionalFields: analysis.optionalFields,
        detectedPlaceholders: analysis.detectedPlaceholders,
        templateType: analysis.templateType,
        complexity: analysis.complexity,
        recommendations: analysis.recommendations,
        missingInformation: analysis.missingInformation,
        autoFillableFields: analysis.autoFillableFields
      },
      success: true
    });

  } catch (error) {
    console.error('❌ GEMINI ANALYSIS: Template analysis failed:', error);
    return NextResponse.json(
      { 
        error: 'Template analysis failed: ' + (error instanceof Error ? error.message : 'Unknown error'),
        fallbackAnalysis: createFallbackAnalysis(markdown)
      },
      { status: 500 }
    );
  }
}

// Load company settings for context
async function loadCompanySettings() {
  try {
    const uploadsDir = join(process.cwd(), 'uploads');
    const settingsPath = join(uploadsDir, 'settings.json');
    const settingsData = await readFile(settingsPath, 'utf-8');
    return JSON.parse(settingsData);
  } catch (error) {
    return getDefaultSettings();
  }
}

function getDefaultSettings() {
  return {
    companyInfo: {
      companyName: 'QuantumRhino',
      contactName: 'Chase Vazquez',
      email: '<EMAIL>',
      phone: '+****************',
      address: '123 Innovation Drive, Tech City, TC 12345',
      website: 'https://quantumrhino.com'
    },
    projectDefaults: {
      defaultProjectType: 'Web Application Development',
      defaultHourlyRate: '150',
      defaultTimeline: '8-12 weeks',
      defaultPaymentTerms: '50% upfront, 50% on completion'
    }
  };
}

// Analyze template with Gemini AI
async function analyzeTemplateWithGemini(markdown: string, settings: any) {
  const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

  const analysisPrompt = `
🔍 **INTELLIGENT TEMPLATE STRUCTURE ANALYSIS**

You are an expert document analyst. Analyze this SOW (Statement of Work) template to understand its STRUCTURE and FIELD REQUIREMENTS only.

**CRITICAL INSTRUCTIONS:**
- IGNORE ALL EXISTING CONTENT/TEXT in the template - treat it as placeholder content that will be replaced
- IGNORE SIGNATURE SECTIONS - do not analyze signature lines, signature blocks, or signature-related content
- Focus ONLY on identifying field structures, placeholders, and document organization
- Extract field requirements based on placeholder patterns and document sections, NOT the actual content
- Treat ALL existing text as sample/placeholder content that will be completely replaced
- Generate field requirements based on what each placeholder/section is MEANT TO REPRESENT, not what it currently contains
- Skip any signature areas, signature lines, or signature-related fields in your analysis

**TEMPLATE STRUCTURE TO ANALYZE:**
${markdown}

**COMPANY CONTEXT (for auto-fillable field identification):**
- Company: ${settings.companyInfo?.companyName || 'QuantumRhino'}
- Contact: ${settings.companyInfo?.contactName || 'Chase Vazquez'}
- Default Rate: $${settings.projectDefaults?.defaultHourlyRate || '150'}/hour
- Default Timeline: ${settings.projectDefaults?.defaultTimeline || '8-12 weeks'}

**COMPLETE FIELD REFERENCE - All possible data points our SOW generator can collect:**

**USER/VENDOR INFORMATION (Auto-fillable from settings):**
- userCompanyName: Service provider company name
- userContactName: Service provider contact person
- userEmail: Service provider email address
- userPhone: Service provider phone number
- userAddress: Service provider business address
- userTitle: Service provider job title/position
- userDepartment: Service provider department

**CLIENT INFORMATION (User must provide):**
- clientName: Client contact person name
- clientEmail: Client email address
- clientCompany: Client company/organization name
- clientPhone: Client phone number
- clientAddress: Client business address
- clientTitle: Client job title/position
- clientDepartment: Client department

**PROJECT BASICS (User must provide):**
- projectName: Name/title of the project
- projectType: Type of project (web development, consulting, etc.)
- projectDescription: Detailed description of project scope and objectives

**TIMELINE & SCHEDULING:**
- startDate: Project start date
- endDate: Project end date
- duration: Project duration (auto-calculated or custom)
- milestones: Array of project milestones and deadlines

**BUDGET & PRICING:**
- hourlyRate: Hourly billing rate
- estimatedHours: Total estimated hours for project
- totalBudget: Total project budget (auto-calculated or custom)
- paymentSchedule: Array of payment installments with amounts and due dates
- depositAmount: Initial deposit amount
- depositDueDate: Deposit due date
- finalPaymentAmount: Final payment amount
- finalPaymentDueDate: Final payment due date

**PROJECT DELIVERABLES & REQUIREMENTS:**
- deliverables: Array of specific project deliverables
- requirements: Array of project requirements and specifications
- additionalRequirements: Additional custom requirements or notes

**LEGAL & PROCESS TERMS:**
- changeRequestProcess: How changes to scope will be handled
- communicationPlan: Communication protocols and schedules
- qualityAssurance: Quality assurance and testing procedures
- supportAndMaintenance: Post-project support terms
- intellectualProperty: Intellectual property ownership terms
- confidentiality: Confidentiality and NDA terms
- terminationClause: Contract termination conditions

**ANALYSIS FOCUS:**
Use this complete field reference to identify which specific data points the template requires. Match template placeholders and sections to these exact field names for ultra-accurate detection.

**ANALYSIS REQUIREMENTS:**

1. **STRUCTURAL FIELD ANALYSIS** - Map template sections to our field reference:
   - Find placeholders like [CLIENT_NAME], {PROJECT_NAME}, [CLIENT_COMPANY], etc.
   - **ALSO LOOK FOR VENDOR/SERVICE PROVIDER PLACEHOLDERS**: [VENDOR_NAME], [COMPANY_NAME], [PROVIDER_CONTACT], [YOUR_NAME], [YOUR_COMPANY], [SERVICE_PROVIDER], [CONTRACTOR], etc.
   - Match each placeholder/section to the exact field names from our complete field reference above
   - Identify document sections that require specific types of information
   - Map template structure to our predefined data categories (User Info, Client Info, Project Details, etc.)

2. **REQUIRED FIELDS** - Information that MUST be provided by the user:
   - Client-specific information (clientName, clientCompany, clientEmail, etc.)
   - Project-specific details (projectName, projectDescription, projectType, etc.)
   - Timeline information (startDate, endDate, milestones if template has timeline sections)
   - Budget details (totalBudget, hourlyRate if template has pricing sections)
   - Custom content that varies per project (deliverables, requirements, etc.)

3. **OPTIONAL FIELDS** - Information that would enhance the SOW:
   - Additional client details (clientTitle, clientDepartment, clientPhone, clientAddress)
   - Enhanced project specifications (additionalRequirements, specific deliverables)
   - Legal terms (changeRequestProcess, intellectualProperty, confidentiality, etc.)
   - Process details (communicationPlan, qualityAssurance, supportAndMaintenance)

4. **AUTO-FILLABLE FIELDS** - Information available from company settings:
   - **CRITICAL**: Always detect user/vendor information fields even if they have placeholder content
   - All user/vendor information (userCompanyName, userContactName, userEmail, userPhone, userAddress, userTitle, userDepartment)
   - Look for vendor/service provider sections in templates - these should be categorized as "User Information"
   - Standard rates and business information
   - Current dates, standard clauses, default values

5. **TEMPLATE CLASSIFICATION**:
   - Template type based on structure (consulting, development, design, etc.)
   - Complexity level based on number and types of fields required
   - Document sections and organization pattern

**RESPONSE FORMAT (JSON):**
{
  "requiredFields": [
    {
      "id": "clientName",
      "title": "Client Contact Name",
      "description": "Primary contact person at the client company - required to replace [CLIENT_NAME] placeholder",
      "placeholder": "[CLIENT_NAME] or {CLIENT_NAME}",
      "category": "Client Information",
      "priority": "high",
      "examples": ["John Smith", "Sarah Johnson", "Michael Chen"],
      "inputType": "text",
      "validation": "required",
      "structuralContext": "Found in header/signature sections, referenced throughout document",
      "formField": "clientName"
    },
    {
      "id": "projectName",
      "title": "Project Name",
      "description": "Name or title of the project - required to replace [PROJECT_NAME] placeholder",
      "placeholder": "[PROJECT_NAME] or {PROJECT_NAME}",
      "category": "Project Details",
      "priority": "high",
      "examples": ["Website Redesign", "Mobile App Development", "E-commerce Platform"],
      "inputType": "text",
      "validation": "required",
      "structuralContext": "Project title section, referenced throughout document",
      "formField": "projectName"
    },
    {
      "id": "projectDescription",
      "title": "Project Description",
      "description": "Detailed description of project scope and objectives",
      "category": "Project Details",
      "priority": "high",
      "examples": ["Develop a responsive e-commerce website with payment integration"],
      "inputType": "textarea",
      "validation": "required",
      "structuralContext": "Project scope section",
      "formField": "projectDescription"
    }
  ],
  "optionalFields": [
    {
      "id": "clientTitle",
      "title": "Client Job Title",
      "description": "Job title or position of the client contact person",
      "category": "Client Information",
      "priority": "medium",
      "examples": ["Marketing Director", "CTO", "Project Manager"],
      "formField": "clientTitle"
    },
    {
      "id": "deliverables",
      "title": "Project Deliverables",
      "description": "Specific deliverables and outputs for the project",
      "category": "Project Details",
      "priority": "medium",
      "examples": ["Responsive website", "Admin dashboard", "User documentation"],
      "formField": "deliverables"
    }
  ],
  "autoFillableFields": [
    {
      "id": "userCompanyName",
      "title": "Service Provider Company Name",
      "value": "${settings.companyInfo?.companyName}",
      "source": "company_settings",
      "placeholder": "[VENDOR_NAME] or {COMPANY_NAME}",
      "structuralContext": "Vendor information section",
      "formField": "userCompanyName",
      "category": "User Information"
    },
    {
      "id": "userContactName",
      "title": "Service Provider Contact Name",
      "value": "${settings.companyInfo?.contactName}",
      "source": "company_settings",
      "placeholder": "[VENDOR_CONTACT] or {CONTACT_NAME}",
      "structuralContext": "Vendor contact section",
      "formField": "userContactName",
      "category": "User Information"
    },
    {
      "id": "userEmail",
      "title": "Service Provider Email",
      "value": "${settings.companyInfo?.contactEmail}",
      "source": "company_settings",
      "placeholder": "[YOUR_EMAIL] or {VENDOR_EMAIL}",
      "structuralContext": "Vendor contact information",
      "formField": "userEmail",
      "category": "User Information"
    },
    {
      "id": "userPhone",
      "title": "Service Provider Phone",
      "value": "${settings.companyInfo?.contactPhone}",
      "source": "company_settings",
      "placeholder": "[YOUR_PHONE] or {VENDOR_PHONE}",
      "structuralContext": "Vendor contact information",
      "formField": "userPhone",
      "category": "User Information"
    }
  ],
  "detectedPlaceholders": ["CLIENT_NAME", "PROJECT_NAME", "START_DATE"],
  "templateType": "software_development_sow",
  "complexity": "moderate",
  "structuralSections": [
    "Header/Title Section",
    "Parties Information",
    "Project Scope",
    "Timeline & Milestones",
    "Payment Terms",
    "Legal Clauses"
  ],
  "recommendations": [
    "Focus on client-specific information first - this populates most placeholders",
    "Define project scope clearly as it drives timeline and budget calculations",
    "Gather payment preferences early as they affect contract terms"
  ],
  "fieldMapping": {
    "CLIENT_NAME": "clientName",
    "PROJECT_NAME": "projectName",
    "START_DATE": "startDate",
    "CLIENT_COMPANY": "clientCompany",
    "PROJECT_DESCRIPTION": "projectDescription",
    "TOTAL_BUDGET": "totalBudget",
    "HOURLY_RATE": "hourlyRate"
  }
}

**CRITICAL ANALYSIS PRINCIPLES:**
- IGNORE all existing content - analyze only the structural requirements
- IGNORE signature sections, signature lines, and signature-related content completely
- Focus on placeholder patterns and document sections, not current text
- **ALWAYS DETECT USER/VENDOR INFORMATION**: Look for service provider, vendor, contractor, or "your company" sections
- **COMMON USER INFO PLACEHOLDERS**: [VENDOR_NAME], [YOUR_COMPANY], [SERVICE_PROVIDER], [CONTRACTOR_NAME], [PROVIDER_CONTACT], [YOUR_NAME], [YOUR_EMAIL], [YOUR_PHONE], [YOUR_ADDRESS]
- USE EXACT FIELD NAMES from our complete field reference (clientName, projectName, userCompanyName, etc.)
- Map each template requirement to our specific form field names for ultra-accurate detection
- Identify what information TYPES are needed, not what content currently exists
- **CATEGORIZE CORRECTLY**: Vendor/service provider info = "User Information", Client info = "Client Information"
- Include "formField" property in each field object to map to our exact form field names
- Provide structural context for each field requirement
- Skip signature areas in your field analysis
- Prioritize fields that appear in multiple template sections or are referenced frequently
`;

  try {
    const result = await model.generateContent(analysisPrompt);
    const response = await result.response;
    const text = response.text();

    console.log('🤖 GEMINI ANALYSIS: Raw response length:', text.length);

    // Extract JSON from response
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No valid JSON found in Gemini response');
    }

    const analysisData = JSON.parse(jsonMatch[0]);
    
    // Validate and enhance the analysis
    return enhanceAnalysis(analysisData, markdown);

  } catch (error) {
    console.error('❌ GEMINI ANALYSIS: Failed to analyze template:', error);
    throw error;
  }
}

// Enhance analysis with additional processing
function enhanceAnalysis(analysis: any, markdown: string) {
  // Add detected placeholders from markdown if not provided
  if (!analysis.detectedPlaceholders || analysis.detectedPlaceholders.length === 0) {
    analysis.detectedPlaceholders = extractPlaceholders(markdown);
  }

  // Ensure required fields have proper structure with form field mapping
  analysis.requiredFields = analysis.requiredFields?.map((field: any) => ({
    id: field.id || field.formField || field.title?.toLowerCase().replace(/\s+/g, '_'),
    title: field.title,
    description: field.description,
    placeholder: field.placeholder,
    category: field.category || 'General',
    priority: field.priority || 'medium',
    examples: field.examples || [],
    inputType: field.inputType || 'text',
    validation: field.validation || 'optional',
    formField: field.formField || field.id, // Ensure we have form field mapping
    structuralContext: field.structuralContext
  })) || [];

  // Ensure optional fields have proper structure
  analysis.optionalFields = analysis.optionalFields?.map((field: any) => ({
    id: field.id || field.formField || field.title?.toLowerCase().replace(/\s+/g, '_'),
    title: field.title,
    description: field.description,
    category: field.category || 'General',
    priority: field.priority || 'low',
    examples: field.examples || [],
    formField: field.formField || field.id,
    structuralContext: field.structuralContext
  })) || [];

  // Ensure auto-fillable fields have proper structure
  analysis.autoFillableFields = analysis.autoFillableFields?.map((field: any) => ({
    id: field.id || field.formField || field.title?.toLowerCase().replace(/\s+/g, '_'),
    title: field.title,
    value: field.value,
    source: field.source || 'company_settings',
    placeholder: field.placeholder,
    formField: field.formField || field.id,
    structuralContext: field.structuralContext,
    category: field.category || 'User Information'
  })) || [];

  // Always include basic user information fields as auto-fillable (SOW templates typically need vendor info)
  const basicUserFields = [
    {
      id: 'userCompanyName',
      title: 'Service Provider Company Name',
      value: 'Auto-filled from settings',
      source: 'company_settings',
      placeholder: '[VENDOR_NAME] or [COMPANY_NAME]',
      formField: 'userCompanyName',
      structuralContext: 'Vendor information section',
      category: 'User Information'
    },
    {
      id: 'userContactName',
      title: 'Service Provider Contact Name',
      value: 'Auto-filled from settings',
      source: 'company_settings',
      placeholder: '[VENDOR_CONTACT] or [YOUR_NAME]',
      formField: 'userContactName',
      structuralContext: 'Vendor contact section',
      category: 'User Information'
    },
    {
      id: 'userEmail',
      title: 'Service Provider Email',
      value: 'Auto-filled from settings',
      source: 'company_settings',
      placeholder: '[YOUR_EMAIL] or [VENDOR_EMAIL]',
      formField: 'userEmail',
      structuralContext: 'Vendor contact information',
      category: 'User Information'
    },
    {
      id: 'userPhone',
      title: 'Service Provider Phone',
      value: 'Auto-filled from settings',
      source: 'company_settings',
      placeholder: '[YOUR_PHONE] or [VENDOR_PHONE]',
      formField: 'userPhone',
      structuralContext: 'Vendor contact information',
      category: 'User Information'
    }
  ];

  // Add basic user fields if they're not already detected
  basicUserFields.forEach(basicField => {
    const exists = analysis.autoFillableFields.some((field: any) => field.formField === basicField.formField);
    if (!exists) {
      analysis.autoFillableFields.push(basicField);
    }
  });

  // Add completion estimate
  analysis.estimatedCompletionTime = estimateCompletionTime(analysis);

  return analysis;
}

// Extract placeholders from markdown
function extractPlaceholders(markdown: string): string[] {
  const placeholders = new Set<string>();
  const patterns = [
    /\{([A-Z_]+)\}/g,
    /\[([A-Z_]+)\]/g,
    /\$\{([A-Z_]+)\}/g,
    /\{\{([A-Z_]+)\}\}/g,
  ];

  patterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(markdown)) !== null) {
      placeholders.add(match[1]);
    }
  });

  return Array.from(placeholders);
}

// Estimate completion time based on analysis
function estimateCompletionTime(analysis: any): string {
  const requiredCount = analysis.requiredFields?.length || 0;
  const optionalCount = analysis.optionalFields?.length || 0;
  const totalFields = requiredCount + optionalCount;

  if (totalFields <= 5) return '2-3 minutes';
  if (totalFields <= 10) return '5-7 minutes';
  if (totalFields <= 15) return '8-12 minutes';
  return '15+ minutes';
}

// Create fallback analysis if Gemini fails
function createFallbackAnalysis(markdown: string) {
  const placeholders = extractPlaceholders(markdown);
  
  return {
    requiredFields: [
      {
        id: 'client_name',
        title: 'Client Name',
        description: 'Primary contact person at the client company',
        category: 'Client Information',
        priority: 'high',
        examples: ['John Smith', 'Sarah Johnson'],
        inputType: 'text',
        validation: 'required'
      },
      {
        id: 'project_name',
        title: 'Project Name',
        description: 'Name or title of the project',
        category: 'Project Information',
        priority: 'high',
        examples: ['Website Redesign', 'Mobile App Development'],
        inputType: 'text',
        validation: 'required'
      }
    ],
    optionalFields: [],
    autoFillableFields: [],
    detectedPlaceholders: placeholders,
    templateType: 'general_sow',
    complexity: 'moderate',
    recommendations: ['Fill out client information first', 'Define project scope clearly'],
    missingInformation: ['Client details', 'Project requirements'],
    estimatedCompletionTime: '5-7 minutes'
  };
}
